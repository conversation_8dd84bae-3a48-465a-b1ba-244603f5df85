/**
 * @license
 * Copyright 2021 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

.flow-vars {
  --app-font-size: 12px;
  --base-spacing: 16px;
  --bold-weight: 500;
  --category-summary-font-size: 18px;
  --double-base-spacing: calc(var(--base-spacing) * 2);
  --gather-mode-icon-size: 16px;
  --header-adjacent-title-font-size: 14px;
  --header-adjacent-title-width: minmax(var(--min-text-width), 1fr);
  --header-current-description-font-size: 12px;
  --header-current-title-font-size: 20px;
  --header-margin: minmax(0px, calc(var(--base-spacing) * 4));
  --half-base-spacing: calc(var(--base-spacing) / 2);
  --min-text-width: 20ch;
  --dialog-border-color: var(--color-gray-700);
  --separator-color: var(--color-gray-300);
  --sidebar-flow-step-navigation-font-size: 14px;
  --sidebar-selected-bg-color: #DDEEFF;
  --sidebar-summary-font-size: 14px;
  --sidebar-title-font-size: 16px;
  --sidebar-width: 320px;
  --summary-flow-step-link-font-size: 14px;
  --summary-navigation-header-font-size: 12px;
  --summary-header-margin: 52px;
  --summary-subtitle-font-size: 12px;
  --summary-title-font-size: 20px;
  --summary-tooltip-box-shadow-color: rgba(0, 0, 0, 0.25);
  --summary-tooltip-line-height: 16px;
  --topbar-button-font-size: 14px;
  --topbar-button-size: 45px;
  --topbar-title-font-size: 14px;
  --dialog-title-font-size: 20px;
}

.App {
  display: grid;
  grid-template-areas:
    "topbar topbar"
    "sidebar content";
  grid-template-columns: min-content auto;
  grid-template-rows: min-content auto;
  height: 100vh;
  max-width: 100vw;
  font-size: var(--app-font-size);
}
.App--collapsed {
  grid-template-columns: 0px auto;
}
.App--collapsed .Sidebar {
  display: none;
}

@media print {
  .App {
    grid-template-rows: 0px auto !important;
    grid-template-columns: 0px auto !important;
  }

  .Topbar {
    display: none !important;
  }

  .Sidebar {
    display: none !important;
  }

  .Content {
    overflow-y: visible !important;
  }

  .SummaryFlow {
    grid-template-columns: repeat(7, min-content) !important;
  }
}

.Topbar {
  position: relative;
  padding-right: var(--base-spacing);
  grid-area: topbar;
  display: flex;
  align-items: center;
  border-bottom: 1px solid var(--separator-color);
}

.TopbarButton {
  height: var(--topbar-button-size);
  min-width: var(--topbar-button-size);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  cursor: pointer;
  background: none;
  color: inherit;
  border: none;
  font: inherit;
  outline: inherit;
  font-size: var(--topbar-button-font-size);
  margin-right: var(--base-spacing);
}

.Topbar__logo {
  display: flex;
  margin-right: var(--half-base-spacing);
}

.Topbar__title {
  font-size: var(--topbar-title-font-size);
  font-weight: var(--bold-weight);
  margin-right: var(--double-base-spacing);
}

.Topbar__help-label {
  display: flex;
  align-items: center;
}
.Topbar__help-label > :first-child {
  margin-right: var(--half-base-spacing);
}

.Content {
  grid-area: content;
  overflow-y: auto;
}

.FlowSegment {
  height: 100%;
  width: var(--gather-mode-icon-size);
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 0 18px;
  color: var(--color-blue-A700);
}

.FlowSegment__top-line, .FlowSegment__bottom-line {
  width: 2px;
  flex-grow: 1;
  background-color: var(--color-blue-A700);
}

.Separator {
  height: 1px;
  width: 100%;
  background-color: var(--separator-color);
}

.Sidebar {
  grid-area: sidebar;
  border-right: 1px solid var(--separator-color);
  width: var(--sidebar-width);
  overflow-x: hidden;
  overflow-y: auto;
}
.Sidebar a {
  color: var(--color-gray-700);
  text-decoration: none;
}

.Sidebar--current {
  background-color: var(--sidebar-selected-bg-color);
  color: var(--color-blue-A700) !important;
  font-weight: var(--bold-weight);
}

.SidebarHeader {
  margin: 26px var(--base-spacing);
}
.SidebarHeader__title {
  font-size: var(--sidebar-title-font-size);
  font-weight: var(--bold-weight);
  width: max-content;
}
.SidebarHeader__date {
  color: var(--color-gray-700);
  width: max-content;
}

.SidebarRuntimeSettings {
  margin: var(--base-spacing);
  padding: var(--half-base-spacing);
  background-color: var(--color-gray-50);
  color: var(--color-gray-700);
  border-radius: 3px;
}

.SidebarRuntimeSettings__item {
  display: flex;
  align-items: center;
  flex-direction: row;
}

.SidebarRuntimeSettings__item--icon {
  margin-right: 8px;
  display: flex;
  height: 16px;
  width: 16px;
  align-items: center;
  justify-content: center;
}

.SidebarSummary {
  width: 100%;
  display: flex;
  padding: var(--half-base-spacing) 0px;
  align-items: center;
  margin: var(--half-base-spacing) 0px;
}

.SidebarSummary__icon {
  display: flex;
  height: 16px;
  width: 16px;
  align-items: center;
  justify-content: center;
  /* Flex column-gap workaround for safari */
  margin: 0px 18px;
}

.SidebarSummary__label {
  font-size: var(--sidebar-summary-font-size);
}

.SidebarFlow {
  margin: var(--half-base-spacing) 0px;
}

.SidebarFlowStep {
  display: flex;
  padding-right: var(--base-spacing);
}

.SidebarFlowStep__label {
  grid-column: 2;
  margin: var(--half-base-spacing) 0px;
  justify-self: left;
  overflow-x: hidden;
  text-overflow: ellipsis;
}
.SidebarFlowStep__label--navigation {
  font-size: var(--sidebar-flow-step-navigation-font-size);
}

.SidebarFlowSeparator > .FlowSegment {
  height: var(--half-base-spacing);
}

.Summary {
  display: grid;
  grid-template-columns: minmax(5%, 1fr) auto minmax(5%, 1fr);
  justify-content: start;
  width: 100%;
}
.Summary > * {
  grid-column: 2;
}
.Summary > .Separator {
  grid-column: 1 / span 3;
}

.SummaryHeader {
  margin: var(--summary-header-margin) 0px;
}

.SummaryHeader__title {
  font-size: var(--summary-title-font-size);
  font-weight: var(--bold-weight);
  line-height: 28px;
  margin: 0 0 calc(0.25 * var(--base-spacing));
}

.SummaryHeader__subtitle {
  font-size: var(--summary-subtitle-font-size);
  line-height: 16px;
}

.SummarySectionHeader {
  margin-top: var(--base-spacing);
}

.SummarySectionHeader__content {
  margin: var(--base-spacing) 0px;
  text-transform: uppercase;
}

.SummaryFlow {
  display: grid;
  max-width: var(--report-content-max-width);
  grid-auto-rows: min-content;
  grid-template-columns: min-content min-content minmax(var(--min-text-width), max-content) 1fr 1fr 1fr 1fr;
  align-items: center;
  margin: var(--base-spacing) 0px;
  --gauge-circle-size: 48px;
}
.SummaryFlow .lh-fraction__content {
  min-width: unset;
}

/* Hide top segment of sidebar flow */
.SidebarFlow > :first-child .FlowSegment > .FlowSegment__top-line {
  background-color: transparent;
}

/* Hide end segments of summary flow */
.SummaryFlow > :first-child > .FlowSegment > .FlowSegment__top-line {
  background-color: transparent;
}
.SummaryFlow > :last-child > .FlowSegment > .FlowSegment__bottom-line {
  background-color: transparent;
}

/* Hide segment for first navigation header */
.SummaryFlow > :first-child .SummaryNavigationHeader .FlowSegment__bottom-line {
  background-color: transparent;
}
.SummaryFlow > :first-child .SummaryNavigationHeader .FlowSegment__top-line {
  background-color: transparent;
}

.SummaryFlowStep {
  display: contents;
}
.SummaryFlowStep .FlowSegment {
  grid-column: 2;
}
.SummaryFlowStep .FlowStepThumbnail {
  margin: var(--half-base-spacing);
}

.FlowStepThumbnail {
  align-self: center;
  justify-self: center;
  object-fit: cover;
  object-position: top left;
  border: 1px solid var(--color-gray-700);
  border-radius: 3px;
  padding: 2px;
  box-sizing: content-box !important;
}

.SummaryFlowStep__label {
  grid-column: 3;
  margin: var(--half-base-spacing);
  width: 100%;
  overflow-x: hidden;
  text-overflow: ellipsis;
}

.SummaryFlowStep__mode {
  color: var(--color-blue-A700);
  font-weight: var(--bold-weight);
}

.SummaryFlowStep__link {
  font-size: var(--summary-flow-step-link-font-size);
  line-height: 20px;
  color: var(--color-gray-800);
  text-decoration: underline;
  width: 100%;
  overflow-x: hidden;
}

.SummaryCategory__content {
  position: relative;
}
.SummaryCategory__content:hover,
.SummaryCategory__content:focus-within {
  background-color: var(--color-gray-100);
}
.SummaryCategory__content:hover .SummaryTooltip,
.SummaryCategory__content:focus-within .SummaryTooltip {
  display: block;
}

.SummaryTooltip {
  display: none;
  position: absolute;
  min-width: 200%;
  max-width: 300%;
  width: max-content;
  background-color: var(--report-background-color);
  border: 1px solid var(--color-gray-900);
  border-radius: 3px;
  padding: var(--base-spacing);
  right: 0;
  line-height: var(--summary-tooltip-line-height);
  box-shadow: 0px 4px 4px var(--summary-tooltip-box-shadow-color);
  z-index: 1;
}

.SummaryTooltip__title {
  font-weight: var(--bold-weight);
}

.SummaryTooltip__url {
  overflow-x: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-top: calc(0.25 * var(--base-spacing));
  margin-bottom: calc(0.75 * var(--base-spacing));
  color: var(--color-gray-700);
}

.SummaryTooltip__category {
  font-weight: var(--bold-weight);
  display: flex;
  align-items: center;
  margin-top: var(--half-base-spacing);
}

.SummaryTooltip__category-title {
  line-height: 24px;
  flex-grow: 1;
}

.SummaryTooltip__rating--pass {
  color: var(--color-pass);
}
.SummaryTooltip__rating--average {
  color: var(--color-average);
}
.SummaryTooltip__rating--fail,
.SummaryTooltip__rating--error {
  color: var(--color-fail);
}

.SummaryTooltip__fraction {
  color: var(--color-gray-700);
}

.SummaryTooltip__informative {
  margin-top: calc(0.25 * var(--base-spacing));
  color: var(--color-gray-700);
}

.SummaryTooltipAudits__title {
  line-height: 24px;
  margin-top: calc(0.75 * var(--base-spacing));
  font-weight: var(--bold-weight);
}

.SummaryTooltipAudit {
  display: flex;
  margin: calc(0.25 * var(--base-spacing)) 0px;
  color: var(--color-gray-700);
  --score-icon-size: 10px;
}
.SummaryTooltipAudit code {
  color: var(--snippet-color);
}
.SummaryTooltipAudit::before {
  content: '';
  height: var(--score-icon-size);
  width: var(--score-icon-size);
  margin-top: calc((var(--summary-tooltip-line-height) - var(--score-icon-size)) / 2);
  margin-right: var(--half-base-spacing);
}
.SummaryTooltipAudit--pass::before {
  background-color: var(--color-pass);
  border-radius: 50%;
}
.SummaryTooltipAudit--average::before {
  background-color: var(--color-average);
}
.SummaryTooltipAudit--fail::before {
  border-left: calc(var(--score-icon-size) / 2) solid transparent;
  border-right: calc(var(--score-icon-size) / 2) solid transparent;
  border-bottom: var(--score-icon-size) solid var(--color-fail);
}

.SummaryNavigationHeader {
  font-size: var(--summary-navigation-header-font-size);
  line-height: 16px;
  display: contents;
}

.SummaryNavigationHeader__url,
.SummaryNavigationHeader__category {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--half-base-spacing);
  background-color: var(--color-gray-100);
  color: var(--color-gray-700);
  border-top: 1px solid var(--color-gray-300);
  text-align: center;
}
.SummaryNavigationHeader__url {
  justify-content: start;
  text-align: left;
}
.SummaryNavigationHeader__url > a {
  color: inherit;
  overflow-x: hidden;
  text-overflow: ellipsis;
}

.SummaryFlowStep__separator {
  display: contents;
}
.SummaryFlowStep__separator > .Separator {
  grid-column-end: span 5;
}

.SummaryCategory__null {
  align-self: center;
  justify-self: center;
  height: 2px;
  width: 10px;
  margin: auto;
  background-color: var(--color-gray-700);
}

.Header {
  display: grid;
  grid-template-areas:
    ".        .          .             current-tn    .             .          ."
    "prev-seg prev-tn    prev-tn       current-tn    next-tn       next-tn    next-seg"
    ".        prev-title .             current-tn    .             next-title ."
    ".        prev-title current-title current-title current-title next-title .";
  grid-template-rows: 1fr auto 1fr auto;
  grid-template-columns: var(--header-margin) var(--header-adjacent-title-width) auto auto auto var(--header-adjacent-title-width) var(--header-margin);
  align-items: center;
  margin: var(--double-base-spacing) 0px;
}
.Header a {
  color: var(--color-gray-800);
}
.Header > .Header__segment:first-child {
  grid-area: prev-seg;
}
.Header > .Header__segment:last-child {
  grid-area: next-seg;
}

.Header__prev-thumbnail {
  display: flex;
  align-items: center;
  grid-area: prev-tn;
}
.Header__current-thumbnail {
  display: flex;
  align-items: center;
  grid-area: current-tn;
}
.Header__next-thumbnail {
  display: flex;
  align-items: center;
  grid-area: next-tn;
}
.Header__prev-thumbnail .Header__segment,
.Header__next-thumbnail .Header__segment {
  flex-grow: 1;
}

.Header__segment {
  height: 2px;
  background-color: var(--color-blue-A700);
}

.HeaderThumbnail {
  position: relative;
  display: flex;
  margin: var(--base-spacing) 0px;
}
.HeaderThumbnail--main .FlowStepThumbnail {
  border: 2px solid var(--color-blue-A700);
}
.HeaderThumbnail--prev .HeaderThumbnail__icon {
  top: 50%;
  left: 100%;
}
.HeaderThumbnail--next .HeaderThumbnail__icon {
  top: 50%;
  left: 0;
}
.HeaderThumbnail--main .HeaderThumbnail__icon {
  top: 100%;
  left: 50%;
}

.HeaderThumbnail__icon {
  position: absolute;
  display: flex;
  padding: calc(var(--base-spacing) * 0.25);
  background-color: var(--report-background-color);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  color: var(--color-blue-A700)
}

.Header__prev-title {
  grid-area: prev-title;
  text-align: left;
  justify-self: start;
}

.Header__next-title {
  grid-area: next-title;
  text-align: right;
  justify-self: end;
}

.Header__prev-title,
.Header__next-title {
  font-size: var(--header-adjacent-title-font-size);
  align-self: start;
  overflow-x: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

.Header__current-title {
  grid-area: current-title;
  text-align: center;
  font-size: var(--header-current-title-font-size);
  font-weight: var(--bold-weight);
  overflow-x: hidden;
  text-overflow: ellipsis;
  margin: 0px var(--half-base-spacing);
}

.Header__current-description {
  text-align: center;
  font-size: var(--header-current-description-font-size);
  font-weight: normal;
}

.HelpDialog {
  position: absolute;
  top: 100%;
  right: var(--base-spacing);
  z-index: 100;
  max-width: 720px;

  background: var(--color-white);
  color: var(--color-gray-800);
  border-radius: 3px;
  border: 1px solid var(--dialog-border-color);
  box-shadow: 0px 4px 12px var(--summary-tooltip-box-shadow-color);

  padding: var(--base-spacing) calc(var(--base-spacing) * 1.5);
}

.HelpDialog__title {
  display: flex;
  align-items: center;

  font-size: var(--dialog-title-font-size);
  font-weight: var(--bold-weight);

  margin-bottom: calc(var(--base-spacing) * 2);
}

.HelpDialog__close {
  background: none;
  border: none;
  cursor: pointer;
  line-height: 0;
}

.HelpDialog__columns {
  display: grid;
  grid-auto-flow: column;
  grid-template-rows: repeat(4, auto);
  grid-template-columns: repeat(3, 1fr);
  grid-column-gap: var(--base-spacing);
}

.HelpDialogColumn {
  display: contents;
}

.HelpDialogColumn__header-title {
  font-size: var(--category-summary-font-size);
  font-weight: var(--bold-weight);
}

.HelpDialogColumn__legend-label {
  color: var(--color-blue-A700);
  font-weight: var(--bold-weight);
}

.HelpDialogColumn ul {
  padding-left: var(--base-spacing);
}

.HelpDialogColumn__use-cases p,
.HelpDialogColumn__categories p {
  font-weight: var(--bold-weight);
  color: var(--color-gray-700);
}

.HelpDialogColumnTimeline {
  display: flex;
  align-items: center;

  height: var(--double-base-spacing);
  color: var(--color-blue-A700);
  margin-right: calc(var(--base-spacing) * -1);
}

.HelpDialogColumn:last-child .HelpDialogColumnTimeline {
  margin-right: 0;
}

.HelpDialogColumnTimeline__line {
  height: 2px;
  flex-grow: 1;

  background: var(--color-blue-A700);
}
