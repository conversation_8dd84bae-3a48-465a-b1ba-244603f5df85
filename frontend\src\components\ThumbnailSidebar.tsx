import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import PageThumbnail from './PageThumbnail'
import { getAllThumbnails } from '../services/api'
import './ThumbnailSidebar.css'

interface ThumbnailSidebarProps {
    fileId: number
    totalPages: number
    currentPage: number
    onPageChange: (pageIndex: number) => void
}

const ThumbnailSidebar: React.FC<ThumbnailSidebarProps> = ({
    fileId,
    totalPages,
    currentPage,
    onPageChange
}) => {
    const [thumbnails, setThumbnails] = useState<string[]>([])
    const [loading, setLoading] = useState(true)
    const [error, setError] = useState<string | null>(null)

    useEffect(() => {
        const loadThumbnails = async () => {
            try {
                setLoading(true)
                setError(null)

                console.log('ThumbnailSidebar: Loading thumbnails for fileId:', fileId, 'totalPages:', totalPages)
                const response = await getAllThumbnails(fileId)
                console.log('ThumbnailSidebar: Received thumbnails:', response.thumbnails.length)
                setThumbnails(response.thumbnails)
            } catch (err) {
                console.error('Error loading thumbnails:', err)
                setError('Failed to load thumbnails')
                // Create empty array for fallback
                setThumbnails(new Array(totalPages).fill(''))
            } finally {
                setLoading(false)
            }
        }

        if (fileId !== undefined && totalPages > 0) {
            console.log('ThumbnailSidebar: Starting thumbnail load for fileId:', fileId, 'totalPages:', totalPages)
            loadThumbnails()
        } else {
            console.log('ThumbnailSidebar: Not loading thumbnails - fileId:', fileId, 'totalPages:', totalPages)
        }
    }, [fileId, totalPages])

    const handlePageClick = (pageIndex: number) => {
        onPageChange(pageIndex)
    }

    if (loading) {
        return (
            <div className="thumbnail-sidebar">
                <div className="sidebar-header">
                    <h3 className="sidebar-title">Pages</h3>
                    <div className="loading-indicator">
                        <div className="loading-spinner"></div>
                        <span>Loading thumbnails...</span>
                    </div>
                </div>
            </div>
        )
    }

    if (error) {
        return (
            <div className="thumbnail-sidebar">
                <div className="sidebar-header">
                    <h3 className="sidebar-title">Pages</h3>
                    <div className="error-message">
                        <span className="error-icon">⚠️</span>
                        <span>{error}</span>
                    </div>
                </div>
                <div className="thumbnails-grid">
                    {Array.from({ length: totalPages }, (_, index) => (
                        <PageThumbnail
                            key={index}
                            pageIndex={index}
                            isActive={index === currentPage}
                            onClick={() => handlePageClick(index)}
                        />
                    ))}
                </div>
            </div>
        )
    }

    return (
        <motion.div
            className="thumbnail-sidebar"
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.3 }}
        >
            <div className="sidebar-header">
                <h3 className="sidebar-title">Pages</h3>
                <div className="page-counter">
                    {currentPage + 1} of {totalPages}
                </div>
            </div>

            <div className="thumbnails-grid">
                {Array.from({ length: totalPages }, (_, index) => (
                    <PageThumbnail
                        key={index}
                        pageIndex={index}
                        isActive={index === currentPage}
                        thumbnail={thumbnails[index]}
                        onClick={() => handlePageClick(index)}
                        isLoading={loading}
                    />
                ))}
            </div>
        </motion.div>
    )
}

export default ThumbnailSidebar
